
user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
     map $http_upgrade $connection_upgrade {
        default upgrade;
        ''      close;
    }

    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;
    #gzip  on;

    ssl_certificate /root/ssl/20200421.xyz_ecc/fullchain.cer;
    ssl_certificate_key /root/ssl/20200421.xyz_ecc/20200421.xyz.key;

    include /etc/nginx/conf.d/*.conf;
}
